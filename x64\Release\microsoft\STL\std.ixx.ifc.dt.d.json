{"Version": "1.2", "Data": {"Source": "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\modules\\std.ixx", "ProvidedModule": "std", "Includes": ["c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\sal.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\concurrencysal.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vadefs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\ctype.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wctype.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\errno.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\fenv.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\float.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\inttypes.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stdint.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\limits.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\locale.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_math.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\setjmp.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\signal.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stdarg.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stddef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_stdio_config.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdlib.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_malloc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_search.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdlib.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\string.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memory.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memcpy_s.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_string.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstring.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\time.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wtime.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\uchar.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wchar.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wconio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wdirect.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_share.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wprocess.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\stat.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\types.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wctype.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\algorithm", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\yvals_core.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xkeycheck.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xmemory", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdint", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdlib", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\limits", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cfloat", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\climits", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cwchar", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdio", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xtr1common", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\intrin0.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\intrin0.inl.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\new", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\exception", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\yvals.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\crtdbg.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_new_debug.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_new.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\crtdefs.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\use_ansi.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\type_traits", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstddef", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\malloc.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_exception.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\eh.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_terminate.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xatomic.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xutility", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_iter_core.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\utility", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\initializer_list", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\concepts", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\compare", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstring", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\tuple", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\optional", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xsmf_control.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\any", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\typeinfo", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_typeinfo.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\array", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\atomic", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xatomic_wait.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xthreads.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xtimec.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ctime", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\barrier", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\bit", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_bit_utils.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\bitset", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\iosfwd", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xstring", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_sanitizer_annotate_container.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xpolymorphic_allocator.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\charconv", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xbit_ops.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xcharconv.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xerrc.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xcharconv_ryu.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xcharconv_ryu_tables.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xcharconv_tables.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\chrono", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_chrono.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ratio", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\system_error", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_system_error_abi.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cerrno", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stdexcept", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xcall_once.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xfilesystem_abi.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_tzdb.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cmath", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\forward_list", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\istream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ostream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ios", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xlocnum", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\iterator", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\streambuf", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xiosbase", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\share.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xlocale", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\memory", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xfacet", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xlocinfo", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_xlocinfo_types.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cctype", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\clocale", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_filebuf.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_print.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\format", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_format_ucd_tables.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\locale", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xlocbuf", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xlocmes", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xlocmon", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xloctime", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\sstream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\string", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vector", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\iomanip", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\codecvt", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\complex", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ymath.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\emmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\mmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\condition_variable", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\mutex", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\thread", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\process.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_startup.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_startup.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stop_token", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\coroutine", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\deque", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\execution", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\numeric", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\queue", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ranges", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_int128.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\span", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\string_view", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\expected", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\filesystem", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\fstream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\functional", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\unordered_map", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xhash", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\list", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xnode_handle.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\future", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ppltasks.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\pplwin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\pplinterface.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ppltaskscheduler.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\crtdefs.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\pplcancellation_token.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\intrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\immintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\wmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\nmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\smmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\tmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\pmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\zmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ammintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\iostream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\latch", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\map", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xtree", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\memory_resource", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\numbers", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\print", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\random", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\regex", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\scoped_allocator", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\semaphore", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\set", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\shared_mutex", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\source_location", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\spanstream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stack", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stacktrace", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stdfloat", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\strstream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\syncstream", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\typeindex", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\unordered_set", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\valarray", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\variant", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\version", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cassert", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\assert.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cfenv", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cinttypes", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\csetjmp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\csignal", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdarg", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cuchar", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cwctype"], "ImportedModules": [], "ImportedHeaderUnits": []}}