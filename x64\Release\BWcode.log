﻿  Assembling include\syscalls_masm.asm...
  Dllmain.cpp
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(21,1): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2552,1):
  参见“STATUS_TIMEOUT”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(22,1): warning C4005: “STATUS_PENDING”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2553,1):
  参见“STATUS_PENDING”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(316,9): error C2059: 语法错误:“if”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(316,53): error C2238: 意外的标记位于“;”之前
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(318,9): error C2059: 语法错误:“case”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(318,33): error C2334: “:”的前面有意外标记；跳过明显的函数体
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(329,9): error C2059: 语法错误:“return”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(277,23): error C3861: “GetKernel32Base”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(283,46): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(296,26): error C3861: “GetNtdllBaseInternal”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(298,67): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(299,65): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(300,61): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(301,49): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(302,35): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(303,69): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(306,45): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(307,47): error C3861: “GetProcAddressManual”: 找不到标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(330,5): error C2059: 语法错误:“}”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(330,5): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(333,60): error C2143: 语法错误: 缺少“;”(在“{”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(333,60): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(348,1): error C2059: 语法错误:“private”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(446,1): error C2059: 语法错误:“}”
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(446,1): error C2143: 语法错误: 缺少“;”(在“}”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(798,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(547,30): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(630,26): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(796,31): error C2039: "GetCurrentProcessHandle": 不是 "APIResolver" 的成员
  C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(223,7):
  参见“APIResolver”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(794,41): error C2672: “nullgate::syscalls::SCall”: 未找到匹配的重载函数
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(147,12):
  可能是“NTSTATUS nullgate::syscalls::SCall(uint64_t,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(794,41):
  	未满足关联约束
  		C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(146,14):
  		计算结果为 false 的概念“std::invocable<CusNtProtectVirtualMemory,PVOID*&&,SIZE_T*&&,int&&,ULONG*&&>”
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\concepts(262,16):
  			“invoke”: 未找到匹配的重载函数
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1735,19):
  				可能是“unknown-type std::invoke(_Callable &&,_Ty1 &&,_Types2 &&...) noexcept(<expr>)”
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1729,19):
  				或    “unknown-type std::invoke(_Callable &&) noexcept(<expr>)”
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(124,12):
  或    “NTSTATUS nullgate::syscalls::SCall(const std::string &,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  	“初始化”: 无法从“uint64_t”转换为“const std::string &”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  		原因如下: 无法从“uint64_t”转换为“const std::string”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  		“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string”: 没有重载函数可以转换所有参数类型
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2558,5):
  			可能是“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”: 无法将参数 1 从“uint64_t”转换为“std::nullptr_t”
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  					仅空指针常数可转换为 nullptr_t
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2545,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”
          with
          [
              _Elem=char
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”: 无法将参数 1 从“uint64_t”转换为“const _Elem *const ”
          with
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  					从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(3092,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”: 无法将参数 1 从“uint64_t”转换为“std::initializer_list<_Elem>”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  					转换要求第二个用户定义的转换运算符或构造函数
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2909,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Ty &,const unsigned __int64,const unsigned __int64,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2763,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::from_range_t,_Rng &&,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2575,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(_Iter,_Iter,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2569,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const unsigned __int64,const _Elem,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2552,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const ,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(795,43):
  			尝试匹配参数列表“(uint64_t)”时
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(816,31): error C2039: "GetCurrentProcessHandle": 不是 "APIResolver" 的成员
  C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(223,7):
  参见“APIResolver”的声明
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(814,32): error C2672: “nullgate::syscalls::SCall”: 未找到匹配的重载函数
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(147,12):
  可能是“NTSTATUS nullgate::syscalls::SCall(uint64_t,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(814,32):
  	未满足关联约束
  		C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(146,14):
  		计算结果为 false 的概念“std::invocable<CusNtProtectVirtualMemory,PVOID*&&,SIZE_T*&&,ULONG&,ULONG*&&>”
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\concepts(262,16):
  			“invoke”: 未找到匹配的重载函数
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1735,19):
  				可能是“unknown-type std::invoke(_Callable &&,_Ty1 &&,_Types2 &&...) noexcept(<expr>)”
  				C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\type_traits(1729,19):
  				或    “unknown-type std::invoke(_Callable &&) noexcept(<expr>)”
  C:\Userfile\ccode\BWcode\BWcode\include\syscalls.hpp(124,12):
  或    “NTSTATUS nullgate::syscalls::SCall(const std::string &,Ts &&...)”
  	C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  	“初始化”: 无法从“uint64_t”转换为“const std::string &”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  		原因如下: 无法从“uint64_t”转换为“const std::string”
  		C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  		“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string”: 没有重载函数可以转换所有参数类型
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2558,5):
  			可能是“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::nullptr_t)”: 无法将参数 1 从“uint64_t”转换为“std::nullptr_t”
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  					仅空指针常数可转换为 nullptr_t
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2545,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”
          with
          [
              _Elem=char
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const )”: 无法将参数 1 从“uint64_t”转换为“const _Elem *const ”
          with
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  					从整型类型转换为指针类型需要 reinterpret_cast、C 样式转换或带圆括号的函数样式强制转换
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(3092,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  				C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  				“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::initializer_list<_Elem>,const _Alloc &)”: 无法将参数 1 从“uint64_t”转换为“std::initializer_list<_Elem>”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
          and
          [
              _Elem=char
          ]
  					C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  					转换要求第二个用户定义的转换运算符或构造函数
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2909,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Ty &,const unsigned __int64,const unsigned __int64,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2763,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(std::from_range_t,_Rng &&,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2575,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(_Iter,_Iter,const _Alloc &)”
          with
          [
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2569,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const unsigned __int64,const _Elem,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\include\xstring(2552,5):
  			或    “std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string(const _Elem *const ,const _Alloc &)”
          with
          [
              _Elem=char,
              _Alloc=std::allocator<char>
          ]
  			C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(815,43):
  			尝试匹配参数列表“(uint64_t)”时
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(916,13): error C2065: “pGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(916,34): error C2146: 语法错误: 缺少“;”(在标识符“fpGetSystemDirectoryA”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(916,34): error C2065: “fpGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(916,59): error C2065: “pGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(917,17): error C2146: 语法错误: 缺少“;”(在标识符“pGetProcAddr”的前面)
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(919,17): error C2065: “fpGetSystemDirectoryA”: 未声明的标识符
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(921,21): error C3861: “fpGetSystemDirectoryA”: 找不到标识符
  obfuscation.cpp
  syscalls.cpp
